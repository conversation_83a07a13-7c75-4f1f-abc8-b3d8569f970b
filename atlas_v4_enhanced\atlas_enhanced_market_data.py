"""
A.T.L.A.S. Enhanced Market Data Manager
Multi-source data aggregation with intelligent fallback and caching
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import pandas as pd
import numpy as np
import yfinance as yf
from atlas_rate_limiter import rate_limiter, APIProvider

logger = logging.getLogger(__name__)


@dataclass
class MarketQuote:
    """Standardized market quote"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    high: float
    low: float
    open: float
    timestamp: datetime
    source: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'price': self.price,
            'change': self.change,
            'change_percent': self.change_percent,
            'volume': self.volume,
            'high': self.high,
            'low': self.low,
            'open': self.open,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source
        }


@dataclass
class HistoricalData:
    """Standardized historical data"""
    symbol: str
    data: pd.DataFrame
    timeframe: str
    source: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'data': self.data.to_dict('records'),
            'timeframe': self.timeframe,
            'source': self.source,
            'timestamp': self.timestamp.isoformat()
        }


class EnhancedMarketDataManager:
    """Enhanced market data manager with multiple sources and intelligent fallback"""
    
    def __init__(self):
        self.data_sources = {
            'fmp': self._get_fmp_data,
            'alpaca': self._get_alpaca_data,
            'yfinance': self._get_yfinance_data,
            'polygon': self._get_polygon_data,
            'alpha_vantage': self._get_alpha_vantage_data
        }
        
        # Source priority (higher number = higher priority)
        # Only yfinance is enabled for now (others require valid API keys)
        self.source_priority = {
            'yfinance': 10,  # Primary source
            'fmp': 1,        # Disabled - requires API key
            'alpaca': 1,     # Disabled - requires API key
            'polygon': 1,    # Disabled - requires API key
            'alpha_vantage': 1  # Disabled - requires API key
        }
        
        # Source health tracking
        self.source_health = {source: 1.0 for source in self.data_sources}
        self.source_last_success = {source: time.time() for source in self.data_sources}
        self.source_failure_count = {source: 0 for source in self.data_sources}
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'cache_hits': 0,
            'fallback_usage': 0,
            'average_response_time': 0.0,
            'source_usage': {source: 0 for source in self.data_sources}
        }
        
        # Local cache for ultra-fast access
        self.quote_cache = {}
        self.historical_cache = {}
        self.cache_ttl = {}
        
        # Start rate limiter
        asyncio.create_task(rate_limiter.start_processing())
    
    def _update_source_health(self, source: str, success: bool):
        """Update source health based on success/failure"""
        if success:
            self.source_health[source] = min(1.0, self.source_health[source] + 0.1)
            self.source_last_success[source] = time.time()
            self.source_failure_count[source] = 0
        else:
            self.source_health[source] = max(0.1, self.source_health[source] - 0.2)
            self.source_failure_count[source] += 1
    
    def _get_source_score(self, source: str) -> float:
        """Calculate source score based on priority and health"""
        base_score = self.source_priority[source]
        health_multiplier = self.source_health[source]
        
        # Penalize sources that haven't succeeded recently
        time_penalty = min(1.0, (time.time() - self.source_last_success[source]) / 3600)  # 1 hour
        
        return base_score * health_multiplier * (1 - time_penalty * 0.5)
    
    def _get_ordered_sources(self) -> List[str]:
        """Get data sources ordered by score (best first)"""
        sources = list(self.data_sources.keys())
        sources.sort(key=self._get_source_score, reverse=True)
        return sources
    
    async def _get_fmp_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Financial Modeling Prep - DISABLED due to API key requirements"""
        # FMP requires valid API key - skip for now to avoid 401 errors
        logger.debug(f"FMP data source disabled for {symbol} - requires valid API key")
        return None
    
    async def _get_alpaca_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Alpaca - DISABLED due to API key requirements"""
        # Alpaca requires valid API key - skip for now to avoid 403 errors
        logger.debug(f"Alpaca data source disabled for {symbol} - requires valid API key")
        return None
    
    async def _get_yfinance_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Yahoo Finance with aggressive rate limiting and graceful degradation"""
        try:
            cache_key = f"yfinance:{symbol}:{data_type}"

            # Check for very recent cache to avoid hitting rate limits
            if cache_key in self.quote_cache or cache_key in self.historical_cache:
                cached_time = self.cache_ttl.get(cache_key, 0)
                cache_duration = 300 if data_type == 'historical' else 120  # 5 min for historical, 2 min for quotes

                if time.time() - cached_time < cache_duration:
                    logger.debug(f"Using cached yfinance data for {symbol} (avoiding rate limits)")
                    if data_type == 'quote':
                        return self.quote_cache.get(cache_key)
                    else:
                        return self.historical_cache.get(cache_key)

            # Check if we've been rate limited recently
            rate_limit_key = f"yfinance_rate_limit"
            if rate_limit_key in self.cache_ttl:
                last_rate_limit = self.cache_ttl[rate_limit_key]
                if time.time() - last_rate_limit < 300:  # 5 minute cooldown after rate limit
                    logger.warning(f"YFinance rate limited recently, skipping {symbol}")
                    return None

            # Aggressive rate limiting - much longer delays
            await asyncio.sleep(5.0)  # 5 second delay between requests

            # Create ticker with timeout
            ticker = yf.Ticker(symbol)

            if data_type == 'quote':
                logger.warning(f"YFinance rate limited - cannot get quote for {symbol}")
                return None

            elif data_type == 'historical':
                logger.info(f"Attempting to get historical data for {symbol}")

                try:
                    # Try to get historical data with very conservative approach
                    hist = ticker.history(period="30d", interval="1d", timeout=10)

                    if not hist.empty:
                        # Process the data
                        hist = hist.reset_index()
                        hist.columns = [col.lower() for col in hist.columns]

                        # Ensure required columns
                        required_columns = ['open', 'high', 'low', 'close', 'volume']
                        if all(col in hist.columns for col in required_columns):
                            historical = HistoricalData(
                                symbol=symbol,
                                data=hist,
                                timeframe='1d',
                                source='yfinance',
                                timestamp=datetime.now()
                            )

                            # Cache the result
                            self.historical_cache[cache_key] = historical
                            self.cache_ttl[cache_key] = time.time()

                            logger.info(f"Successfully got yfinance historical data for {symbol}")
                            return historical

                    # If we get here, no data available
                    logger.warning(f"No historical data available for {symbol}")
                    return None

                except Exception as hist_error:
                    if "429" in str(hist_error) or "Too Many Requests" in str(hist_error):
                        # Mark that we've been rate limited
                        self.cache_ttl[rate_limit_key] = time.time()
                        logger.error(f"YFinance rate limited for {symbol}: {hist_error}")
                    else:
                        logger.error(f"YFinance historical error for {symbol}: {hist_error}")

                    # Return None on error - no fake data
                    return None

            return None

        except Exception as e:
            if "429" in str(e) or "Too Many Requests" in str(e):
                # Mark that we've been rate limited
                rate_limit_key = f"yfinance_rate_limit"
                self.cache_ttl[rate_limit_key] = time.time()
                logger.error(f"YFinance rate limited for {symbol}: {e}")
            else:
                logger.error(f"YFinance data error for {symbol}: {e}")

            # Return None on error - no fake data
            return None
    
    async def _get_polygon_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Polygon.io - DISABLED due to API key requirements"""
        # Polygon requires valid API key - skip for now to avoid 404 errors
        logger.debug(f"Polygon data source disabled for {symbol} - requires valid API key")
        return None

    async def _get_alpha_vantage_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Alpha Vantage - DISABLED due to API key requirements"""
        # Alpha Vantage requires valid API key - skip for now
        logger.debug(f"Alpha Vantage data source disabled for {symbol} - requires valid API key")
        return None
    
    async def get_quote(self, symbol: str, timeout: float = 10.0) -> Optional[MarketQuote]:
        """Get real-time quote with intelligent fallback"""
        start_time = time.time()
        self.metrics['total_requests'] += 1
        
        # Try sources in order of preference
        sources = self._get_ordered_sources()
        
        for source in sources:
            try:
                logger.debug(f"Trying {source} for {symbol} quote")
                
                result = await asyncio.wait_for(
                    self.data_sources[source](symbol, 'quote'),
                    timeout=timeout / len(sources)
                )
                
                if result:
                    self._update_source_health(source, True)
                    self.metrics['successful_requests'] += 1
                    self.metrics['source_usage'][source] += 1
                    
                    response_time = time.time() - start_time
                    self.metrics['average_response_time'] = (
                        (self.metrics['average_response_time'] * (self.metrics['successful_requests'] - 1) + response_time) /
                        self.metrics['successful_requests']
                    )
                    
                    logger.info(f"Successfully got {symbol} quote from {source} in {response_time:.2f}s")
                    return result
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout getting {symbol} quote from {source}")
                self._update_source_health(source, False)
                continue
            
            except Exception as e:
                logger.error(f"Error getting {symbol} quote from {source}: {e}")
                self._update_source_health(source, False)
                continue
        
        logger.error(f"Failed to get quote for {symbol} from all sources")
        return None
    
    async def get_historical_data(self, symbol: str, timeframe: str = '1d', 
                                 timeout: float = 15.0) -> Optional[HistoricalData]:
        """Get historical data with intelligent fallback"""
        start_time = time.time()
        
        # Try sources in order of preference
        sources = self._get_ordered_sources()
        
        for source in sources:
            try:
                logger.debug(f"Trying {source} for {symbol} historical data")
                
                result = await asyncio.wait_for(
                    self.data_sources[source](symbol, 'historical'),
                    timeout=timeout / len(sources)
                )
                
                if result:
                    self._update_source_health(source, True)
                    self.metrics['source_usage'][source] += 1
                    
                    response_time = time.time() - start_time
                    logger.info(f"Successfully got {symbol} historical data from {source} in {response_time:.2f}s")
                    return result
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout getting {symbol} historical data from {source}")
                self._update_source_health(source, False)
                continue
            
            except Exception as e:
                logger.error(f"Error getting {symbol} historical data from {source}: {e}")
                self._update_source_health(source, False)
                continue
        
        logger.error(f"Failed to get historical data for {symbol} from all sources")
        return None
    
    async def get_multiple_quotes(self, symbols: List[str], timeout: float = 30.0) -> Dict[str, Optional[MarketQuote]]:
        """Get multiple quotes efficiently with batch processing"""
        start_time = time.time()
        results = {}
        
        # Create tasks for all symbols
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(self.get_quote(symbol, timeout / len(symbols)))
            tasks.append((symbol, task))
        
        # Wait for all tasks with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.warning("Timeout in batch quote request")
        
        # Collect results
        for symbol, task in tasks:
            try:
                if task.done():
                    result = task.result()
                    results[symbol] = result
                else:
                    task.cancel()
                    results[symbol] = None
            except Exception as e:
                logger.error(f"Error getting quote for {symbol}: {e}")
                results[symbol] = None
        
        response_time = time.time() - start_time
        successful_count = len([r for r in results.values() if r is not None])
        
        logger.info(f"Batch quote request completed: {successful_count}/{len(symbols)} successful in {response_time:.2f}s")
        
        return results
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self.metrics,
            'source_health': self.source_health,
            'source_priority': self.source_priority,
            'cache_size': len(self.quote_cache) + len(self.historical_cache)
        }


# Global enhanced market data manager
enhanced_market_data = EnhancedMarketDataManager()
