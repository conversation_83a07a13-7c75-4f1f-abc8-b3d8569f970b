{"total_files": 57, "dependency_graph": {"analyze_scanner_config": ["atlas_realtime_scanner", "sp500_symbols"], "apply_scanner_fixes": ["atlas_lee_method", "atlas_realtime_scanner"], "atlas_ai_core": ["atlas_causal_reasoning", "atlas_alternative_data", "atlas_video_processor", "atlas_data_fusion", "sp500_symbols", "atlas_quantum_optimizer", "atlas_explainable_ai", "models", "config", "atlas_web_search_service", "atlas_theory_of_mind", "atlas_autonomous_agents", "atlas_global_markets", "atlas_grok_integration", "atlas_image_analyzer"], "atlas_alternative_data": ["models"], "atlas_autonomous_agents": ["models"], "atlas_causal_reasoning": ["atlas_grok_integration", "models"], "atlas_database": ["models"], "atlas_data_fusion": ["atlas_alternative_data", "atlas_video_processor", "models", "atlas_grok_integration", "atlas_image_analyzer"], "atlas_education": ["atlas_web_search_service", "models", "config"], "atlas_ethical_ai": ["atlas_grok_integration", "models"], "atlas_explainable_ai": ["models"], "atlas_global_markets": ["atlas_grok_integration", "models"], "atlas_grok_integration": ["config", "models"], "atlas_grok_system_integration": ["atlas_ai_core", "atlas_trading_core", "models", "atlas_realtime_scanner", "atlas_market_core", "grok_performance_optimizer", "atlas_lee_method", "grok_resilience_manager", "atlas_grok_integration"], "atlas_grok_trading_strategies": ["grok_performance_optimizer", "atlas_trading_core", "models", "atlas_grok_system_integration", "grok_resilience_manager", "atlas_grok_integration", "atlas_risk_core"], "atlas_image_analyzer": ["atlas_grok_integration", "models"], "atlas_lee_method": ["sp500_symbols", "atlas_web_search_service", "atlas_market_core", "config"], "atlas_market_core": ["atlas_alert_manager", "models", "config", "atlas_web_search_service", "sp500_symbols"], "atlas_ml_analytics": ["models", "atlas_grok_integration", "config"], "atlas_monitoring": ["models", "config"], "atlas_news_insights_engine": ["atlas_web_search_service", "config", "atlas_progress_tracker", "models"], "atlas_options": ["models", "config"], "atlas_orchestrator": ["atlas_news_insights_engine", "atlas_utils", "atlas_education", "atlas_trading_core", "models", "config", "atlas_market_core", "atlas_web_search_service", "atlas_database", "atlas_ai_core", "sp500_symbols", "atlas_options", "atlas_lee_method", "atlas_grok_integration", "atlas_risk_core"], "atlas_privacy_learning": ["atlas_grok_integration", "models"], "atlas_quantum_optimizer": ["models"], "atlas_realtime": ["sp500_symbols", "models", "config"], "atlas_realtime_monitor": ["models"], "atlas_realtime_scanner": ["atlas_performance_monitor", "atlas_alert_manager", "models", "config", "atlas_market_core", "atlas_lee_method", "sp500_symbols"], "atlas_risk_core": ["atlas_web_search_service", "models", "config"], "atlas_security": ["models", "config"], "atlas_server": ["atlas_progress_tracker", "atlas_orchestrator", "models", "config", "atlas_realtime_scanner", "atlas_lee_method", "sp500_symbols", "atlas_conversation_monitor"], "atlas_startup": ["atlas_lee_method", "models", "config"], "atlas_strategies": ["models", "config"], "atlas_testing": ["atlas_ai_core", "atlas_lee_method", "models", "config"], "atlas_theory_of_mind": ["atlas_grok_integration", "models"], "atlas_trading_core": ["atlas_web_search_service", "models", "config"], "atlas_utils": ["models", "config"], "atlas_video_processor": ["models"], "atlas_web_search_service": ["atlas_grok_integration", "config"], "grok_advanced_features_example": ["atlas_grok_integration"], "grok_performance_optimizer": ["atlas_grok_integration"], "grok_resilience_manager": ["atlas_grok_integration"], "grok_usage_examples": ["atlas_ai_core", "atlas_grok_integration", "atlas_orchestrator", "models"], "news_insights_examples": ["atlas_ai_core", "atlas_news_insights_engine", "atlas_progress_tracker", "atlas_orchestrator"], "validate_fixes": ["atlas_lee_method", "atlas_realtime_scanner"]}, "circular_dependencies": [], "missing_imports": ["pennylane", "sentence_transformers", "xgboost", "asyncpg", "dowhy", "econml.dml", "qutip", "cv2", "cvxpy", "statsmodels.tsa.seasonal", "speech_recognition", "lime", "lime.lime_tabular", "tweepy", "causalml.inference.meta", "shap", "torchvision.transforms", "ccxt", "librosa", "statsmodels.tsa.arima.model", "web3"], "complexity_metrics": {"analyze_scanner_config": {"direct_dependencies": 2, "transitive_dependencies": 10, "external_dependencies": 0, "complexity_score": 7.0}, "apply_scanner_fixes": {"direct_dependencies": 2, "transitive_dependencies": 10, "external_dependencies": 5, "complexity_score": 7.0}, "atlas_ai_core": {"direct_dependencies": 15, "transitive_dependencies": 15, "external_dependencies": 16, "complexity_score": 22.5}, "atlas_alternative_data": {"direct_dependencies": 1, "transitive_dependencies": 1, "external_dependencies": 15, "complexity_score": 1.5}, "atlas_autonomous_agents": {"direct_dependencies": 1, "transitive_dependencies": 1, "external_dependencies": 10, "complexity_score": 1.5}, "atlas_causal_reasoning": {"direct_dependencies": 2, "transitive_dependencies": 3, "external_dependencies": 12, "complexity_score": 3.5}, "atlas_database": {"direct_dependencies": 1, "transitive_dependencies": 1, "external_dependencies": 8, "complexity_score": 1.5}, "atlas_data_fusion": {"direct_dependencies": 5, "transitive_dependencies": 6, "external_dependencies": 13, "complexity_score": 8.0}, "atlas_education": {"direct_dependencies": 3, "transitive_dependencies": 4, "external_dependencies": 7, "complexity_score": 5.0}, "atlas_ethical_ai": {"direct_dependencies": 2, "transitive_dependencies": 3, "external_dependencies": 13, "complexity_score": 3.5}, "atlas_explainable_ai": {"direct_dependencies": 1, "transitive_dependencies": 1, "external_dependencies": 16, "complexity_score": 1.5}, "atlas_global_markets": {"direct_dependencies": 2, "transitive_dependencies": 3, "external_dependencies": 12, "complexity_score": 3.5}, "atlas_grok_integration": {"direct_dependencies": 2, "transitive_dependencies": 2, "external_dependencies": 12, "complexity_score": 3.0}, "atlas_grok_system_integration": {"direct_dependencies": 9, "transitive_dependencies": 24, "external_dependencies": 5, "complexity_score": 21.0}, "atlas_grok_trading_strategies": {"direct_dependencies": 7, "transitive_dependencies": 26, "external_dependencies": 6, "complexity_score": 20.0}, "atlas_image_analyzer": {"direct_dependencies": 2, "transitive_dependencies": 3, "external_dependencies": 18, "complexity_score": 3.5}, "atlas_lee_method": {"direct_dependencies": 4, "transitive_dependencies": 7, "external_dependencies": 14, "complexity_score": 7.5}, "atlas_market_core": {"direct_dependencies": 5, "transitive_dependencies": 6, "external_dependencies": 14, "complexity_score": 8.0}, "atlas_ml_analytics": {"direct_dependencies": 3, "transitive_dependencies": 3, "external_dependencies": 22, "complexity_score": 4.5}, "atlas_monitoring": {"direct_dependencies": 2, "transitive_dependencies": 2, "external_dependencies": 10, "complexity_score": 3.0}, "atlas_news_insights_engine": {"direct_dependencies": 4, "transitive_dependencies": 5, "external_dependencies": 16, "complexity_score": 6.5}, "atlas_options": {"direct_dependencies": 2, "transitive_dependencies": 2, "external_dependencies": 11, "complexity_score": 3.0}, "atlas_orchestrator": {"direct_dependencies": 15, "transitive_dependencies": 27, "external_dependencies": 7, "complexity_score": 28.5}, "atlas_privacy_learning": {"direct_dependencies": 2, "transitive_dependencies": 3, "external_dependencies": 17, "complexity_score": 3.5}, "atlas_quantum_optimizer": {"direct_dependencies": 1, "transitive_dependencies": 1, "external_dependencies": 15, "complexity_score": 1.5}, "atlas_realtime": {"direct_dependencies": 3, "transitive_dependencies": 3, "external_dependencies": 8, "complexity_score": 4.5}, "atlas_realtime_monitor": {"direct_dependencies": 1, "transitive_dependencies": 1, "external_dependencies": 18, "complexity_score": 1.5}, "atlas_realtime_scanner": {"direct_dependencies": 7, "transitive_dependencies": 9, "external_dependencies": 16, "complexity_score": 11.5}, "atlas_risk_core": {"direct_dependencies": 3, "transitive_dependencies": 4, "external_dependencies": 10, "complexity_score": 5.0}, "atlas_security": {"direct_dependencies": 2, "transitive_dependencies": 2, "external_dependencies": 11, "complexity_score": 3.0}, "atlas_server": {"direct_dependencies": 8, "transitive_dependencies": 31, "external_dependencies": 19, "complexity_score": 23.5}, "atlas_startup": {"direct_dependencies": 3, "transitive_dependencies": 8, "external_dependencies": 9, "complexity_score": 7.0}, "atlas_strategies": {"direct_dependencies": 2, "transitive_dependencies": 2, "external_dependencies": 9, "complexity_score": 3.0}, "atlas_testing": {"direct_dependencies": 4, "transitive_dependencies": 19, "external_dependencies": 9, "complexity_score": 13.5}, "atlas_theory_of_mind": {"direct_dependencies": 2, "transitive_dependencies": 3, "external_dependencies": 9, "complexity_score": 3.5}, "atlas_trading_core": {"direct_dependencies": 3, "transitive_dependencies": 4, "external_dependencies": 14, "complexity_score": 5.0}, "atlas_utils": {"direct_dependencies": 2, "transitive_dependencies": 2, "external_dependencies": 10, "complexity_score": 3.0}, "atlas_video_processor": {"direct_dependencies": 1, "transitive_dependencies": 1, "external_dependencies": 15, "complexity_score": 1.5}, "atlas_web_search_service": {"direct_dependencies": 2, "transitive_dependencies": 3, "external_dependencies": 12, "complexity_score": 3.5}, "grok_advanced_features_example": {"direct_dependencies": 1, "transitive_dependencies": 3, "external_dependencies": 5, "complexity_score": 2.5}, "grok_performance_optimizer": {"direct_dependencies": 1, "transitive_dependencies": 3, "external_dependencies": 11, "complexity_score": 2.5}, "grok_resilience_manager": {"direct_dependencies": 1, "transitive_dependencies": 3, "external_dependencies": 9, "complexity_score": 2.5}, "grok_usage_examples": {"direct_dependencies": 4, "transitive_dependencies": 28, "external_dependencies": 4, "complexity_score": 18.0}, "news_insights_examples": {"direct_dependencies": 4, "transitive_dependencies": 28, "external_dependencies": 4, "complexity_score": 18.0}, "validate_fixes": {"direct_dependencies": 2, "transitive_dependencies": 10, "external_dependencies": 5, "complexity_score": 7.0}}, "external_imports": {"apply_scanner_fixes": ["os", "pytz", "asyncio", "datetime", "sys"], "atlas_ai_core": ["enum", "os", "json", "uuid", "logging", "asyncio", "re", "transformers", "httpx", "typing", "openai", "numpy", "datetime", "sys", "dataclasses", "pandas"], "atlas_alert_manager": ["email.mime.multipart", "enum", "time", "json", "asyncio", "logging", "uuid", "requests", "email.mime.text", "datetime", "typing", "dataclasses", "smtplib"], "atlas_alternative_data": ["<PERSON><PERSON><PERSON>", "enum", "asyncio", "json", "logging", "requests", "tweepy", "web3", "ccxt", "numpy", "aiohttp", "datetime", "typing", "dataclasses", "pandas"], "atlas_autonomous_agents": ["enum", "asyncio", "json", "logging", "uuid", "numpy", "datetime", "typing", "dataclasses", "pandas"], "atlas_causal_reasoning": ["enum", "dowhy", "asyncio", "econml.dml", "logging", "numpy", "causalml.inference.meta", "datetime", "typing", "dataclasses", "networkx", "pandas"], "atlas_conversation_monitor": ["enum", "time", "json", "asyncio", "logging", "re", "datetime", "typing", "dataclasses"], "atlas_database": ["os", "json", "asyncio", "logging", "sqlite3", "datetime", "typing", "dataclasses"], "atlas_data_fusion": ["enum", "asyncio", "json", "logging", "transformers", "torch.nn.functional", "numpy", "torch", "datetime", "typing", "dataclasses", "torch.nn", "pandas"], "atlas_education": ["os", "json", "asyncio", "logging", "typing", "datetime", "sys"], "atlas_ethical_ai": ["scipy.stats", "enum", "sklearn.model_selection", "asyncio", "json", "logging", "uuid", "numpy", "datetime", "typing", "dataclasses", "pandas", "sklearn.metrics"], "atlas_explainable_ai": ["shap", "<PERSON><PERSON><PERSON>", "enum", "sklearn.model_selection", "asyncio", "json", "logging", "uuid", "lime", "sklearn.ensemble", "numpy", "lime.lime_tabular", "datetime", "typing", "dataclasses", "pandas"], "atlas_global_markets": ["enum", "yfinance", "asyncio", "json", "logging", "ccxt", "requests", "numpy", "datetime", "typing", "dataclasses", "pandas"], "atlas_grok_integration": ["<PERSON><PERSON><PERSON>", "enum", "time", "json", "asyncio", "logging", "httpx", "datetime", "typing", "dataclasses", "base64", "pydantic"], "atlas_grok_system_integration": ["json", "asyncio", "logging", "datetime", "typing"], "atlas_grok_trading_strategies": ["json", "asyncio", "logging", "numpy", "datetime", "typing"], "atlas_image_analyzer": ["matplotlib.pyplot", "enum", "cv2", "asyncio", "json", "logging", "PIL", "seaborn", "io", "torchvision.transforms", "numpy", "torch", "sklearn.cluster", "datetime", "typing", "dataclasses", "base64", "pandas"], "atlas_lee_method": ["os", "time", "json", "asyncio", "logging", "requests", "pytz", "typing", "numpy", "aiohttp", "datetime", "sys", "dataclasses", "pandas"], "atlas_market_core": ["yfinance", "os", "time", "json", "asyncio", "logging", "requests", "alpaca_trade_api", "typing", "numpy", "aiohttp", "datetime", "sys", "pandas"], "atlas_ml_analytics": ["numpy", "sys", "sklearn.ensemble", "pandas", "asyncio", "logging", "xgboost", "torch.utils.data", "typing", "torch.nn", "sklearn.metrics", "sklearn.model_selection", "transformers", "torch", "scipy.stats", "torch.optim", "os", "statsmodels.tsa.arima.model", "json", "sklearn.preprocessing", "datetime", "statsmodels.tsa.seasonal"], "atlas_monitoring": ["os", "json", "asyncio", "logging", "random", "typing", "datetime", "psutil", "sys", "dataclasses"], "atlas_news_insights_engine": ["enum", "json", "asyncio", "logging", "re", "transformers", "sentence_transformers", "redis.asyncio", "typing", "sklearn.cluster", "numpy", "aiohttp", "datetime", "<PERSON><PERSON><PERSON>", "dataclasses", "asyncpg"], "atlas_options": ["enum", "os", "json", "asyncio", "logging", "random", "typing", "math", "datetime", "sys", "dataclasses"], "atlas_orchestrator": ["os", "json", "asyncio", "logging", "typing", "datetime", "sys"], "atlas_performance_monitor": ["enum", "time", "json", "logging", "queue", "datetime", "threading", "typing", "dataclasses"], "atlas_privacy_learning": ["<PERSON><PERSON><PERSON>", "enum", "sklearn.model_selection", "cryptography", "asyncio", "json", "logging", "uuid", "sklearn.ensemble", "sklearn.preprocessing", "numpy", "torch", "datetime", "typing", "dataclasses", "torch.nn", "pandas"], "atlas_progress_tracker": ["enum", "time", "json", "uuid", "asyncio", "logging", "datetime", "typing", "dataclasses"], "atlas_quantum_optimizer": ["qutip", "enum", "asyncio", "json", "logging", "cvxpy", "pennylane", "scipy.optimize", "numpy", "sklearn.covariance", "math", "datetime", "typing", "dataclasses", "pandas"], "atlas_realtime": ["os", "json", "asyncio", "logging", "random", "typing", "datetime", "sys"], "atlas_realtime_monitor": ["cProfile", "enum", "time", "asyncio", "json", "logging", "uuid", "collections", "numpy", "threading", "pstats", "tracemalloc", "datetime", "psutil", "typing", "dataclasses", "concurrent.futures", "pandas"], "atlas_realtime_scanner": ["os", "time", "json", "asyncio", "logging", "pytz", "typing", "numpy", "queue", "aiohttp", "datetime", "threading", "sys", "dataclasses", "concurrent.futures", "pandas"], "atlas_risk_core": ["os", "json", "asyncio", "logging", "typing", "numpy", "math", "datetime", "sys", "scipy.stats"], "atlas_secrets_manager": ["os", "json", "cryptography.fernet", "logging", "cryptography.hazmat.primitives", "typing", "base64", "cryptography.hazmat.primitives.kdf.pbkdf2", "pathlib"], "atlas_security": ["<PERSON><PERSON><PERSON>", "enum", "os", "json", "asyncio", "logging", "typing", "secrets", "datetime", "sys", "dataclasses"], "atlas_server": ["sys", "asyncio", "fastapi.middleware.cors", "logging", "typing", "time", "fastapi.staticfiles", "fastapi.security", "u<PERSON><PERSON>", "collections", "base64", "os", "json", "dotenv", "<PERSON><PERSON><PERSON>", "datetime", "fastapi.responses", "dataclasses", "pydantic"], "atlas_startup": ["os", "json", "asyncio", "logging", "webbrowser", "typing", "datetime", "sys", "subprocess"], "atlas_strategies": ["enum", "os", "json", "asyncio", "logging", "typing", "datetime", "sys", "dataclasses"], "atlas_terminal_streamer": ["json", "asyncio", "logging", "contextlib", "typing", "io", "queue", "datetime", "threading", "sys"], "atlas_testing": ["os", "time", "json", "asyncio", "logging", "typing", "unittest", "datetime", "sys"], "atlas_theory_of_mind": ["enum", "asyncio", "json", "logging", "numpy", "datetime", "typing", "dataclasses", "pandas"], "atlas_trading_core": ["enum", "os", "yfinance", "json", "uuid", "logging", "re", "asyncio", "random", "alpaca_trade_api", "sys", "datetime", "typing", "dataclasses"], "atlas_utils": ["functools", "time", "os", "asyncio", "json", "logging", "typing", "datetime", "sys", "traceback"], "atlas_video_processor": ["speech_recognition", "enum", "cv2", "asyncio", "json", "logging", "re", "transformers", "numpy", "torch", "librosa", "datetime", "typing", "dataclasses", "pandas"], "atlas_web_search_service": ["enum", "dateutil", "json", "asyncio", "logging", "re", "typing", "aiohttp", "datetime", "<PERSON><PERSON><PERSON>", "dataclasses", "urllib.parse"], "config": ["os", "pydantic_settings", "logging", "typing", "pydantic"], "debug_progress": ["time", "json", "asyncio", "aiohttp", "websockets", "traceback"], "grok_advanced_features_example": ["json", "asyncio", "logging", "datetime", "typing"], "grok_performance_optimizer": ["time", "json", "asyncio", "logging", "typing", "collections", "datetime", "threading", "<PERSON><PERSON><PERSON>", "dataclasses", "concurrent.futures"], "grok_resilience_manager": ["enum", "time", "asyncio", "json", "logging", "random", "datetime", "typing", "dataclasses"], "grok_usage_examples": ["logging", "datetime", "typing", "asyncio"], "models": ["typing", "pydantic", "enum", "datetime"], "news_insights_examples": ["typing", "json", "datetime", "asyncio"], "sp500_symbols": ["re"], "validate_api_endpoints": ["json", "asyncio", "logging", "inspect", "typing", "importlib.util", "pathlib"], "validate_fixes": ["os", "pytz", "asyncio", "datetime", "sys"], "validate_imports": ["os", "asyncio", "json", "logging", "importlib", "typing", "collections", "ast", "sys", "pathlib"]}, "generated_requirements": "Pillow\naiohttp\nalpaca_trade_api\nasyncpg\ncProfile\ncausalml.inference.meta\nccxt\nconcurrent.futures\ncontextlib\ncryptography\ncryptography.fernet\ncryptography.hazmat.primitives\ncryptography.hazmat.primitives.kdf.pbkdf2\nopencv-python\ncvxpy\npython-dateutil\ndotenv\ndowhy\neconml.dml\nemail.mime.multipart\nemail.mime.text\nfastapi\nfastapi.middleware.cors\nfastapi.responses\nfastapi.security\nfastapi.staticfiles\nhttpx\nimportlib.util\nio\nlibrosa\nlime\nlime.lime_tabular\nmatplotlib.pyplot\nnetworkx\nnumpy\nopenai\npandas\npennylane\npstats\npsutil\npydantic\npydantic_settings\npytz\nqutip\nredis.async<PERSON>\nrequests\nscipy.optimize\nscipy.stats\nseaborn\nsecrets\nsentence_transformers\nshap\nsklearn.cluster\nsklearn.covariance\nsklearn.ensemble\nsklearn.metrics\nsklearn.model_selection\nsklearn.preprocessing\nspeech_recognition\nstatsmodels.tsa.arima.model\nstatsmodels.tsa.seasonal\nsubprocess\ntorch\ntorch.nn\ntorch.nn.functional\ntorch.optim\ntorch.utils.data\ntorchvision.transforms\ntraceback\ntracemalloc\ntransformers\ntweepy\nunittest\nurllib.parse\nuvicorn\nweb3\nwebbrowser\nwebsockets\nxgboost\nyfinance", "validation_summary": {"has_circular_deps": false, "has_missing_imports": true, "most_complex_module": "atlas_orchestrator", "total_external_packages": 106}}